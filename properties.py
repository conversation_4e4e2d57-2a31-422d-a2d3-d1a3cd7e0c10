import bpy
from bpy.types import PropertyGroup, Scene, Object, Material
from bpy.props import (
    String<PERSON>roperty,
    BoolProperty,
    IntProperty,
    FloatProperty,
    FloatVectorProperty,
    EnumProperty,
    PointerProperty,
)

class SDFSceneProperties(PropertyGroup):
    """Global SDF scene properties"""
    
    # Node Tree Properties
    def _update_node_tree(self, context):
        from .shaders import SDFRenderer
        SDFRenderer.set_node_tree(self.sdf_node_tree)

    sdf_node_tree: PointerProperty(
        name="SDF Node Tree",
        description="Node tree containing SDF operations",
        type=bpy.types.NodeTree,
        update=_update_node_tree
    )
    


    sdf_show_in_viewport: BoolProperty(
        name="Show in Viewport",
        description="Display SDF in the 3D viewport",
        default=False,
        update=_update_viewport_visibility
    )
    
    sdf_viewport_quality: FloatProperty(
        name="Viewport Quality",
        description="Quality of viewport rendering (higher = better quality, lower = better performance)",
        default=0.5,
        min=0.1,
        max=1.0,
        subtype='FACTOR',
        update=_update_viewport_quality
    )
    
    sdf_max_steps: IntProperty(
        name="Max Steps",
        description="Maximum raymarching steps (higher = better quality, lower = better performance)",
        default=100,
        min=10,
        max=1000,
        update=_update_render_settings
    )
    
    sdf_surface_threshold: FloatProperty(
        name="Surface Threshold",
        description="Distance threshold for surface detection",
        default=0.001,
        min=0.0001,
        max=0.1,
        precision=5,
        update=_update_render_settings
    )
    
    # Legacy properties (kept for backward compatibility)
    viewport_resolution: IntProperty(
        name="Resolution",
        description="Viewport rendering resolution (higher = better quality, lower = better performance)",
        default=256,
        min=32,
        max=1024,
    )
    
    show_wireframe: BoolProperty(
        name="Show Wireframe",
        description="Display wireframe over the SDF surface",
        default=True,
    )
    
    max_steps: IntProperty(
        name="Max Steps",
        description="Maximum raymarching steps (affects quality and performance)",
        default=100,
        min=10,
        max=500,
    )
    
    max_distance: FloatProperty(
        name="Max Distance",
        description="Maximum raymarching distance",
        default=100.0,
        min=1.0,
        max=1000.0,
    )
    
    surface_threshold: FloatProperty(
        name="Surface Threshold",
        description="Distance threshold for surface detection",
        default=0.001,
        min=0.0001,
        max=0.1,
        precision=5,
    )
    
    def _update_viewport_visibility(self, context):
        """Update viewport visibility based on settings"""
        from .shaders import SDFRenderer
        
        if self.sdf_show_in_viewport and self.sdf_node_tree:
            SDFRenderer.set_node_tree(self.sdf_node_tree)
            if not SDFRenderer.is_enabled():
                SDFRenderer.enable()
        else:
            if SDFRenderer.is_enabled():
                SDFRenderer.disable()
    
    def _update_viewport_quality(self, context):
        """Update viewport quality settings"""
        from .shaders import SDFRenderer
        
        # Map 0.1-1.0 to 10-500 steps
        steps = int(10 + (500 - 10) * self.sdf_viewport_quality)
        self.sdf_max_steps = steps
        
        # Update renderer if active
        if self.sdf_show_in_viewport and SDFRenderer.is_enabled():
            SDFRenderer.update_settings(
                max_steps=steps,
                surface_threshold=self.sdf_surface_threshold
            )
    
    def _update_render_settings(self, context):
        """Update renderer settings"""
        from .shaders import SDFRenderer
        
        if self.sdf_show_in_viewport and SDFRenderer.is_enabled():
            SDFRenderer.update_settings(
                max_steps=self.sdf_max_steps,
                surface_threshold=self.sdf_surface_threshold
            )

class SDFObjectProperties(PropertyGroup):
    """SDF object-specific properties"""
    
    is_sdf_object: BoolProperty(
        name="Is SDF Object",
        description="Whether this object is an SDF object",
        default=False,
    )
    
    sdf_type: EnumProperty(
        name="SDF Type",
        description="Type of SDF object",
        items=[
            ('PRIMITIVE', "Primitive", "SDF Primitive"),
            ('NODE_TREE', "Node Tree", "SDF Node Tree"),
            ('MESH', "Mesh", "Converted Mesh"),
        ],
        default='PRIMITIVE',
    )
    
    primitive_type: EnumProperty(
        name="Primitive Type",
        description="Type of SDF primitive",
        items=[
            ('SPHERE', "Sphere", "SDF Sphere"),
            ('BOX', "Box", "SDF Box"),
            ('CYLINDER', "Cylinder", "SDF Cylinder"),
            ('TORUS', "Torus", "SDF Torus"),
            ('CAPSULE', "Capsule", "SDF Capsule"),
            ('CONE', "Cone", "SDF Cone"),
        ],
        default='SPHERE',
    )
    
    # Common primitive properties
    radius: FloatProperty(
        name="Radius",
        description="Radius of the primitive",
        default=1.0,
        min=0.001,
        soft_max=10.0,
    )
    
    size: FloatVectorProperty(
        name="Size",
        description="Size of the primitive",
        default=(1.0, 1.0, 1.0),
        min=0.001,
        soft_max=10.0,
        subtype='XYZ',
        size=3,
    )

class SDFMaterialProperties(PropertyGroup):
    """SDF material properties"""
    
    base_color: FloatVectorProperty(
        name="Base Color",
        description="Base color of the SDF material",
        subtype='COLOR',
        default=(0.8, 0.8, 0.8, 1.0),
        min=0.0,
        max=1.0,
        size=4,
    )
    
    metallic: FloatProperty(
        name="Metallic",
        description="Metallic factor (0 = dielectric, 1 = metallic)",
        default=0.0,
        min=0.0,
        max=1.0,
    )
    
    roughness: FloatProperty(
        name="Roughness",
        description="Roughness factor (0 = smooth, 1 = rough)",
        default=0.5,
        min=0.0,
        max=1.0,
    )

# Store the original classes to prevent re-registration
_classes = None

# Define the property classes to register
classes = (
    SDFSceneProperties,
    SDFObjectProperties,
    SDFMaterialProperties,
)

def register():
    """Register all property classes and add them to the scene and object types."""
    global _classes
    
    # Only register once
    if _classes is not None:
        return True
    
    # Register property classes
    _classes = []
    for cls in classes:
        # Skip if already registered
        if hasattr(bpy.types, cls.__name__):
            try:
                bpy.utils.unregister_class(getattr(bpy.types, cls.__name__))
            except:
                pass
        
        # Register the class
        bpy.utils.register_class(cls)
        _classes.append(cls)
    
    # Add properties to scene and object types
    if hasattr(Scene, 'sdf'):
        del Scene.sdf
    if hasattr(Object, 'sdf'):
        del Object.sdf
    if hasattr(Material, 'sdf'):
        del Material.sdf
    
    Scene.sdf = PointerProperty(type=SDFSceneProperties)
    Object.sdf = PointerProperty(type=SDFObjectProperties)
    Material.sdf = PointerProperty(type=SDFMaterialProperties)
    
    print("Arcane SDF: Properties registered")
    return True

def unregister():
    """Unregister all property classes and remove them from the scene and object types."""
    global _classes
    
    # Remove properties from scene and object types
    if hasattr(Scene, 'sdf'):
        del Scene.sdf
    if hasattr(Object, 'sdf'):
        del Object.sdf
    if hasattr(Material, 'sdf'):
        del Material.sdf
    
    # Unregister property classes
    if _classes is not None:
        for cls in reversed(classes):
            if hasattr(bpy.types, cls.__name__):
                try:
                    bpy.utils.unregister_class(getattr(bpy.types, cls.__name__))
                except:
                    pass
        _classes = None
    
    print("Arcane SDF: Properties unregistered")
    return True
