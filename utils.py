import bpy
import bmesh
import numpy as np
from mathutils import Vector, Matrix
from bpy.types import Operator, Panel, PropertyGroup
from bpy.props import (
    StringProperty,
    BoolProperty,
    IntProperty,
    FloatProperty,
    FloatVectorProperty,
    EnumProperty,
    PointerProperty,
    CollectionProperty,
)

def get_active_sdf_node_tree(context):
    """Get the active SDF node tree from the context"""
    space = context.space_data
    if space and space.type == 'NODE_EDITOR' and space.tree_type == 'SDFNodeTree':
        return space.node_tree
    return None

def create_bounding_box_mesh(obj):
    """Create a bounding box mesh for the given object"""
    bm = bmesh.new()
    bmesh.ops.create_cube(bm, size=1.0)
    
    # Scale the cube to match the object's dimensions
    bm.transform(obj.matrix_world)
    
    # Create a new mesh and link it to the scene
    mesh = bpy.data.meshes.new(name=f"{obj.name}_bbox")
    bm.to_mesh(mesh)
    bm.free()
    
    bbox_obj = bpy.data.objects.new(f"{obj.name}_bbox", mesh)
    bbox_obj.display_type = 'WIRE'
    bbox_obj.parent = obj
    
    return bbox_obj

def register():
    """Register utility classes and functions"""
    pass

def unregister():
    """Unregister utility classes and functions"""
    pass
