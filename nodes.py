import bpy
from bpy.types import Node, <PERSON>de<PERSON><PERSON>, NodeSocket, NodeTreeInterfaceSocket
from bpy.props import (
    StringProperty,
    BoolProperty,
    IntProperty,
    FloatProperty,
    FloatVectorProperty,
    EnumProperty,
    PointerProperty,
    CollectionProperty,
)

# Custom socket type for SDF nodes
class SDFNodeSocket(NodeSocket):
    """SDF node socket type"""
    bl_idname = 'SDFNodeSocket'
    bl_label = "SDF Socket"
    
    default_type: StringProperty(default='FLOAT')
    
    def draw(self, context, layout, node, text):
        layout.label(text=text)
    
    def draw_color(self, context, node):
        return (0.8, 0.8, 1.0, 1.0)

from .sdf_engine import SDF<PERSON>ng<PERSON>, BoundingBox
from mathutils import Vector
import math

# Base class for all SDF nodes
class SDFNode(Node):
    """Base class for all SDF nodes"""
    bl_idname = 'SDFNode'
    bl_label = "SDF Node"
    bl_icon = 'CUBE'
    
    # Cache for evaluated SDF values
    _sdf_cache = {}
    _cache_enabled = True
    _cache_max_size = 1000  # Maximum number of cached values per node
    
    def init(self, context):
        """Initialize the node with default inputs and outputs"""
        pass
    
    def update(self):
        """Update the node when inputs change"""
        # Invalidate cache for this node and its dependents
        self._sdf_cache.clear()
        
        # Update connected nodes
        for output in self.outputs:
            for link in output.links:
                if link.to_node:
                    link.to_node.update()
    
    def _add_to_cache(self, key, value):
        """Add a value to the cache with size limit"""
        if len(self._sdf_cache) >= self._cache_max_size:
            # Remove oldest entries (simple FIFO)
            oldest_key = next(iter(self._sdf_cache))
            del self._sdf_cache[oldest_key]
        self._sdf_cache[key] = value
    
    def evaluate(self, point):
        """
        Evaluate the SDF at the given point.
        
        Args:
            point (Vector): 3D point to evaluate
            
        Returns:
            float: Signed distance value
        """
        cache_key = (id(self), point.x, point.y, point.z)
        
        # Return cached value if available
        if self._cache_enabled and cache_key in self._sdf_cache:
            return self._sdf_cache[cache_key]
        
        # Evaluate the SDF
        distance = self._evaluate_impl(point)
        
        # Cache the result
        if self._cache_enabled:
            self._add_to_cache(cache_key, distance)
            
        return distance
    
    def _evaluate_impl(self, point):
        """
        Implementation of SDF evaluation. Override this in derived classes.
        
        Args:
            point (Vector): 3D point to evaluate
            
        Returns:
            float: Signed distance value
        """
        return 0.0
    
    def bounds(self):
        """
        Get the bounding box that contains this SDF.
        
        Returns:
            BoundingBox: Bounding box containing the SDF
        """
        # Default to infinite bounds if not implemented
        return BoundingBox()
    
    def generate_glsl(self):
        """
        Generate GLSL code for this node.
        
        Returns:
            str: GLSL code that evaluates the SDF at point 'p'
        """
        return "float d = 0.0;"
    
    @classmethod
    def poll(cls, ntree):
        """Only allow this node in SDF node trees"""
        return ntree.bl_idname == 'SDFNodeTree'

# Primitive Nodes
class SDFNode_Sphere(SDFNode):
    bl_idname = 'SDFNode_Sphere'
    bl_label = "Sphere"
    
    radius: FloatProperty(
        name="Radius",
        default=1.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        layout.prop(self, "radius")
    
    def _evaluate_impl(self, point):
        """Evaluate SDF for a sphere"""
        return point.length - self.radius
    
    def bounds(self):
        """Return bounding box for the sphere"""
        r = self.radius
        return BoundingBox(
            Vector((-r, -r, -r)),
            Vector((r, r, r))
        )
    
    def generate_glsl(self):
        return f"float d = length(p) - {self.radius};\n"

class SDFNode_Box(SDFNode):
    bl_idname = 'SDFNode_Box'
    bl_label = "Box"
    
    size: FloatVectorProperty(
        name="Size",
        default=(1.0, 1.0, 1.0),
        subtype='XYZ',
        size=3,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        layout.prop(self, "size")
    
    def _evaluate_impl(self, point):
        """Evaluate SDF for a box"""
        q = Vector((abs(p) for p in point)) - Vector(self.size)
        return max(q, Vector((0, 0, 0))).length + min(max(q.x, q.y, q.z), 0.0)
    
    def bounds(self):
        """Return bounding box for the box"""
        size = Vector(self.size)
        return BoundingBox(-size, size)
    
    def generate_glsl(self):
        return f"vec3 q = abs(p) - vec3{tuple(self.size)};\n" \
               "float d = length(max(q, 0.0)) + min(max(q.x, max(q.y, q.z)), 0.0);\n"

# Operation Nodes
class SDFNode_Union(SDFNode):
    bl_idname = 'SDFNode_Union'
    bl_label = "Union"
    
    smooth_radius: FloatProperty(
        name="Smooth Radius",
        default=0.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.inputs.new('SDFNodeSocket', "A")
        self.inputs.new('SDFNodeSocket', "B")
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        if self.smooth_radius > 0.0:
            layout.prop(self, "smooth_radius")
    
    def _get_input_node(self, socket_name):
        """Get the node connected to the specified input socket"""
        input_socket = self.inputs.get(socket_name)
        if input_socket and input_socket.is_linked:
            return input_socket.links[0].from_node
        return None
    
    def _evaluate_impl(self, point):
        """Evaluate union of two SDFs"""
        node_a = self._get_input_node("A")
        node_b = self._get_input_node("B")
        
        if not node_a and not node_b:
            return 0.0
        elif not node_a:
            return node_b.evaluate(point)
        elif not node_b:
            return node_a.evaluate(point)
        
        d1 = node_a.evaluate(point)
        d2 = node_b.evaluate(point)
        
        if self.smooth_radius > 0.0:
            return SDFEngine.smooth_union(d1, d2, self.smooth_radius)
        return min(d1, d2)
    
    def bounds(self):
        """Return the union of the input bounds"""
        node_a = self._get_input_node("A")
        node_b = self._get_input_node("B")
        
        bounds = BoundingBox()
        if node_a:
            bounds = bounds.union(node_a.bounds())
        if node_b:
            bounds = bounds.union(node_b.bounds())
            
        return bounds
    
    def generate_glsl(self):
        if self.smooth_radius > 0.0:
            return f"float d = smooth_union(a, b, {self.smooth_radius});\n"
        return "float d = min(a, b);\n"

class SDFNode_Subtraction(SDFNode):
    bl_idname = 'SDFNode_Subtraction'
    bl_label = "Subtraction"
    
    smooth_radius: FloatProperty(
        name="Smooth Radius",
        default=0.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.inputs.new('SDFNodeSocket', "A")
        self.inputs.new('SDFNodeSocket', "B")
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        if self.smooth_radius > 0.0:
            layout.prop(self, "smooth_radius")
    
    def _get_input_node(self, socket_name):
        """Get the node connected to the specified input socket"""
        input_socket = self.inputs.get(socket_name)
        if input_socket and input_socket.is_linked:
            return input_socket.links[0].from_node
        return None
    
    def _evaluate_impl(self, point):
        """Evaluate subtraction of two SDFs (A - B)"""
        node_a = self._get_input_node("A")
        node_b = self._get_input_node("B")
        
        if not node_a and not node_b:
            return 0.0
        elif not node_a:
            return -node_b.evaluate(point) if node_b else 0.0
        elif not node_b:
            return node_a.evaluate(point)
        
        d1 = node_a.evaluate(point)
        d2 = -node_b.evaluate(point)  # Invert the second SDF for subtraction
        
        if self.smooth_radius > 0.0:
            # For smooth subtraction, we use smooth_union with inverted second SDF
            return SDFEngine.smooth_union(d1, d2, self.smooth_radius)
        return max(d1, d2)
    
    def bounds(self):
        """Return the bounds of the first input (A)"""
        node_a = self._get_input_node("A")
        return node_a.bounds() if node_a else BoundingBox()
    
    def generate_glsl(self):
        if self.smooth_radius > 0.0:
            return f"float d = smooth_union(a, -b, {self.smooth_radius});\n"
        return "float d = max(a, -b);\n"

class SDFNode_Intersection(SDFNode):
    bl_idname = 'SDFNode_Intersection'
    bl_label = "Intersection"
    
    smooth_radius: FloatProperty(
        name="Smooth Radius",
        default=0.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.inputs.new('SDFNodeSocket', "A")
        self.inputs.new('SDFNodeSocket', "B")
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        if self.smooth_radius > 0.0:
            layout.prop(self, "smooth_radius")
    
    def _get_input_node(self, socket_name):
        """Get the node connected to the specified input socket"""
        input_socket = self.inputs.get(socket_name)
        if input_socket and input_socket.is_linked:
            return input_socket.links[0].from_node
        return None
    
    def _evaluate_impl(self, point):
        """Evaluate intersection of two SDFs"""
        node_a = self._get_input_node("A")
        node_b = self._get_input_node("B")
        
        if not node_a and not node_b:
            return 0.0
        elif not node_a:
            return node_b.evaluate(point) if node_b else 0.0
        elif not node_b:
            return node_a.evaluate(point)
        
        d1 = node_a.evaluate(point)
        d2 = node_b.evaluate(point)
        
        if self.smooth_radius > 0.0:
            # For smooth intersection, we use max with smooth min
            return SDFEngine.smooth_union(-d1, -d2, self.smooth_radius)
        return max(d1, d2)
    
    def bounds(self):
        """Return the intersection of the input bounds"""
        node_a = self._get_input_node("A")
        node_b = self._get_input_node("B")
        
        if not node_a or not node_b:
            return BoundingBox()
            
        # Intersection is the overlapping region of both bounds
        a_bounds = node_a.bounds()
        b_bounds = node_b.bounds()
        
        min_point = Vector((
            max(a_bounds.min.x, b_bounds.min.x),
            max(a_bounds.min.y, b_bounds.min.y),
            max(a_bounds.min.z, b_bounds.min.z)
        ))
        
        max_point = Vector((
            min(a_bounds.max.x, b_bounds.max.x),
            min(a_bounds.max.y, b_bounds.max.y),
            min(a_bounds.max.z, b_bounds.max.z)
        ))
        
        # If no intersection, return empty bounds
        if (min_point.x > max_point.x or 
            min_point.y > max_point.y or 
            min_point.z > max_point.z):
            return BoundingBox()
            
        return BoundingBox(min_point, max_point)
    
    def generate_glsl(self):
        if self.smooth_radius > 0.0:
            return f"float d = smooth_intersection(a, b, {self.smooth_radius});\n"
        return "float d = max(a, b);\n"

class SDFNode_Cylinder(SDFNode):
    bl_idname = 'SDFNode_Cylinder'
    bl_label = "Cylinder"
    
    radius: FloatProperty(
        name="Radius",
        default=1.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    height: FloatProperty(
        name="Height",
        default=2.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        layout.prop(self, "radius")
        layout.prop(self, "height")
    
    def _evaluate_impl(self, point):
        """Evaluate SDF for a cylinder"""
        d = Vector((point.x, point.y, 0)).length - self.radius
        return max(d, abs(point.z) - self.height * 0.5)
    
    def bounds(self):
        """Return bounding box for the cylinder"""
        r = self.radius
        h = self.height * 0.5
        return BoundingBox(
            Vector((-r, -r, -h)),
            Vector((r, r, h))
        )
    
    def generate_glsl(self):
        return f"float d = length(p.xy) - {self.radius};\n" \
               f"d = max(d, abs(p.z) - {self.height * 0.5});\n"

class SDFNode_Torus(SDFNode):
    bl_idname = 'SDFNode_Torus'
    bl_label = "Torus"
    
    major_radius: FloatProperty(
        name="Major Radius",
        default=1.0,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    minor_radius: FloatProperty(
        name="Minor Radius",
        default=0.3,
        min=0.0,
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        layout.prop(self, "major_radius")
        layout.prop(self, "minor_radius")
    
    def _evaluate_impl(self, point):
        """Evaluate SDF for a torus"""
        q = Vector((Vector((point.x, point.z)).length - self.major_radius, point.y))
        return q.length - self.minor_radius
    
    def bounds(self):
        """Return bounding box for the torus"""
        r1 = self.major_radius
        r2 = self.minor_radius
        return BoundingBox(
            Vector((-r1 - r2, -r2, -r1 - r2)),
            Vector((r1 + r2, r2, r1 + r2))
        )
    
    def generate_glsl(self):
        return f"vec2 q = vec2(length(p.xz) - {self.major_radius}, p.y);\n" \
               f"float d = length(q) - {self.minor_radius};\n"

# Transform Nodes
class SDFNode_Translate(SDFNode):
    bl_idname = 'SDFNode_Translate'
    bl_label = "Translate"
    
    offset: FloatVectorProperty(
        name="Offset",
        default=(0.0, 0.0, 0.0),
        subtype='TRANSLATION',
        update=lambda self, context: self.update()
    )
    
    def init(self, context):
        self.inputs.new('SDFNodeSocket', "SDF")
        self.outputs.new('SDFNodeSocket', "SDF")
    
    def draw_buttons(self, context, layout):
        layout.prop(self, "offset")
    
    def _get_input_node(self):
        """Get the node connected to the input socket"""
        input_socket = self.inputs.get("SDF")
        if input_socket and input_socket.is_linked:
            return input_socket.links[0].from_node
        return None
    
    def _evaluate_impl(self, point):
        """Evaluate the translated SDF"""
        node = self._get_input_node()
        if not node:
            return 0.0
            
        # Translate the point in the opposite direction
        translated_point = point - Vector(self.offset)
        return node.evaluate(translated_point)
    
    def bounds(self):
        """Return the translated bounds of the input SDF"""
        node = self._get_input_node()
        if not node:
            return BoundingBox()
            
        # Translate the input bounds by the offset
        bounds = node.bounds()
        offset = Vector(self.offset)
        return BoundingBox(
            bounds.min + offset,
            bounds.max + offset
        )
    
    def generate_glsl(self):
        return f"float d = sdf(p - vec3{tuple(self.offset)});\n"

# Node Tree UI Functions
def draw_node_tree_switcher(layout, context):
    """Draw node tree switcher UI"""
    # Check if context is valid
    if not context or not hasattr(context, 'space_data') or not context.space_data:
        return
        
    if not hasattr(context.space_data, 'node_tree'):
        return
        
    # Check if layout has row method
    if not hasattr(layout, 'row'):
        return
        
    row = layout.row()
    
    # Only show the node tree switcher if we're in the node editor
    if context.space_data.type == 'NODE_EDITOR':
        row.template_ID(context.space_data, "node_tree", new="node.new_geometry_node_group_assign",
                      unlink="node.group_ungroup")
        
        # Add a dropdown to switch between node trees
        if context.space_data.node_tree:
            row.menu("NODE_MT_node_tree_switch_menu", text="", icon='ARROW_LEFTRIGHT')
            
            # Add duplicate and delete buttons
            op = row.operator("node.duplicate_geometry_node_tree_assign", text="", icon='DUPLICATE')
            op.keep_inputs = True
            
            op = row.operator("node.geometry_node_tree_assign", text="", icon='X')
            op.remove = True
    
    # Add a button to create a new node tree if none exists
    if not context.space_data.node_tree:
        row.operator("sdf.node_tree_new", text="New SDF Node Tree")

# SDF Node Tree
class SDFNodeTree(NodeTree):
    """Node tree for SDF operations"""
    bl_idname = 'SDFNodeTree'
    bl_label = "SDF Node Tree"
    bl_icon = 'NODETREE'
    
    # Add custom properties here if needed
    sdf_output_node: StringProperty(
        name="Output Node",
        description="Name of the output node in this tree",
        default=""
    )
    
    # Flag to track if the tree needs to be recompiled
    needs_update: BoolProperty(
        name="Needs Update",
        description="Set to True when the node tree needs to be recompiled",
        default=True
    )
    
    def update(self):
        """Called when the node tree is modified"""
        self.needs_update = True
        
        # Notify the viewport to update
        for window in bpy.context.window_manager.windows:
            for area in window.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
    
    def get_output_node(self):
        """Get the output node of this tree"""
        if self.sdf_output_node and self.sdf_output_node in self.nodes:
            return self.nodes[self.sdf_output_node]
            
        # If no output node is set, try to find one
        for node in self.nodes:
            if node.bl_idname == 'NodeGroupOutput':
                self.sdf_output_node = node.name
                return node
                
        return None

# Registration
classes = (
    SDFNodeSocket,
    SDFNode,
    SDFNode_Sphere,
    SDFNode_Box,
    SDFNode_Cylinder,
    SDFNode_Torus,
    SDFNode_Union,
    SDFNode_Subtraction,
    SDFNode_Intersection,
    SDFNode_Translate,
    SDFNodeTree,
)

# Node categories for the add menu
# Format: (category_id, category_name, category_description, [node_classes])
node_categories = [
    ('SDF_PRIMITIVES', 'SDF Primitives', 'SDF primitive nodes', [
        SDFNode_Sphere,
        SDFNode_Box,
        SDFNode_Cylinder,
        SDFNode_Torus,
    ]),
    ('SDF_OPERATIONS', 'SDF Operations', 'SDF operation nodes', [
        SDFNode_Union,
        SDFNode_Subtraction,
        SDFNode_Intersection,
    ]),
    ('SDF_TRANSFORMS', 'SDF Transforms', 'SDF transform nodes', [
        SDFNode_Translate,
    ]),
]

# Global to store registered node categories
_node_categories = []

def register():
    from bpy.utils import register_class
    
    # Register all node classes
    for cls in classes:
        try:
            register_class(cls)
            print(f"Successfully registered node class: {cls.__name__}")
        except ValueError as e:
            # If class is already registered, unregister it first
            if "already registered" in str(e):
                bpy.utils.unregister_class(cls)
                register_class(cls)
                print(f"Re-registered node class: {cls.__name__}")
            else:
                print(f"Error registering {cls.__name__}: {e}")
                raise
    
    # Register node tree type
    try:
        register_class(SDFNodeTree)
        print(f"Successfully registered node tree: {SDFNodeTree.__name__}")
    except ValueError as e:
        if "already registered" in str(e):
            bpy.utils.unregister_class(SDFNodeTree)
            register_class(SDFNodeTree)
            print(f"Re-registered node tree: {SDFNodeTree.__name__}")
        else:
            print(f"Error registering SDFNodeTree: {e}")
            raise
    
    # Add node tree switcher to the node editor header
    # Temporarily disabled due to compatibility issues
    # if hasattr(bpy.types, 'NODE_HT_header'):
    #     try:
    #         bpy.types.NODE_HT_header.append(draw_node_tree_switcher)
    #     except Exception as e:
    #         print(f"Error adding node tree switcher: {e}")
    
    # Register node categories - simplified approach
    global _node_categories
    _node_categories = []
    
    # Add nodes directly to the add menu
    def draw_add_menu(self, context):
        if context.space_data and hasattr(context.space_data, 'tree_type'):
            if context.space_data.tree_type == 'SDFNodeTree':
                layout = self.layout
                
                # Add primitives
                layout.label(text="Primitives")
                layout.operator("node.add_node", text="Sphere").type = 'SDFNode_Sphere'
                layout.operator("node.add_node", text="Box").type = 'SDFNode_Box'
                layout.operator("node.add_node", text="Cylinder").type = 'SDFNode_Cylinder'
                layout.operator("node.add_node", text="Torus").type = 'SDFNode_Torus'
                
                layout.separator()
                
                # Add operations
                layout.label(text="Operations")
                layout.operator("node.add_node", text="Union").type = 'SDFNode_Union'
                layout.operator("node.add_node", text="Subtraction").type = 'SDFNode_Subtraction'
                layout.operator("node.add_node", text="Intersection").type = 'SDFNode_Intersection'
                
                layout.separator()
                
                # Add transforms
                layout.label(text="Transforms")
                layout.operator("node.add_node", text="Translate").type = 'SDFNode_Translate'
    
    # Add the draw function to NODE_MT_add
    try:
        bpy.types.NODE_MT_add.append(draw_add_menu)
        print("Successfully added SDF nodes to NODE_MT_add")
    except Exception as e:
        print(f"Error adding SDF nodes to menu: {e}")
        import traceback
        traceback.print_exc()

def unregister():
    # Remove node tree switcher from node editor header
    # Temporarily disabled due to compatibility issues
    # if hasattr(bpy.types, 'NODE_HT_header') and hasattr(bpy.types.NODE_HT_header, 'draw_funcs'):
    #     try:
    #         bpy.types.NODE_HT_header.remove(draw_node_tree_switcher)
    #     except:
    #         pass
    
    # Remove draw function from NODE_MT_add
    if hasattr(bpy.types, 'NODE_MT_add'):
        try:
            # Remove our draw function
            for func in list(bpy.types.NODE_MT_add.draw_funcs):
                if 'draw_add_menu' in func.__name__:
                    bpy.types.NODE_MT_add.remove(func)
        except:
            pass
    
    # Unregister node categories
    global _node_categories
    for cls in reversed(_node_categories):
        try:
            bpy.utils.unregister_class(cls)
        except:
            pass
    _node_categories = []
    
    # Unregister all node classes
    from bpy.utils import unregister_class
    for cls in reversed(classes):
        try:
            unregister_class(cls)
        except:
            pass
    
    # Unregister node tree type
    try:
        unregister_class(SDFNodeTree)
    except:
        pass
