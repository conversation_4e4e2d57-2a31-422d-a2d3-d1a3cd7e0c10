import math
import bpy
import mathutils
from mathutils import Vector, Matrix

# Constants
PI = math.pi
TAU = 2.0 * PI
EPSILON = 1e-6
INF = float('inf')

# For spatial acceleration
class BoundingBox:
    """Axis-aligned bounding box for spatial acceleration"""
    __slots__ = ('min', 'max')
    
    def __init__(self, min_point=None, max_point=None):
        self.min = min_point or Vector((-INF, -INF, -INF))
        self.max = max_point or Vector((INF, INF, INF))
    
    @property
    def center(self):
        """Get the center point of the bounding box"""
        return (self.min + self.max) * 0.5
    
    @property
    def size(self):
        """Get the size of the bounding box"""
        return self.max - self.min
    
    def union(self, other):
        """Create a new bounding box that contains both this and another box"""
        if not isinstance(other, BoundingBox):
            return self
        return BoundingBox(
            Vector((min(self.min[i], other.min[i]) for i in range(3))),
            Vector((max(self.max[i], other.max[i]) for i in range(3)))
        )
    
    def contains(self, point):
        """Check if a point is inside the bounding box"""
        return all(self.min[i] <= point[i] <= self.max[i] for i in range(3))
    
    def intersects(self, ray_origin, ray_direction):
        """Check if a ray intersects with the bounding box"""
        tmin = -INF
        tmax = INF
        
        for i in range(3):
            if abs(ray_direction[i]) < EPSILON:
                # Ray is parallel to slab. No hit if origin not within slab
                if ray_origin[i] < self.min[i] or ray_origin[i] > self.max[i]:
                    return False
            else:
                # Compute intersection t value of ray with near and far plane of slab
                t1 = (self.min[i] - ray_origin[i]) / ray_direction[i]
                t2 = (self.max[i] - ray_origin[i]) / ray_direction[i]
                tmin, tmax = max(tmin, min(t1, t2)), min(tmax, max(t1, t2))
                
                if tmax < tmin:
                    return False
                    
        return True

class SDFEngine:
    """Core SDF engine implementing distance functions and operations"""
    
    # ==============================================
    # Basic Primitives
    # ==============================================
    
    @staticmethod
    def sphere(p, radius=1.0):
        """
        Signed distance function for a sphere centered at origin.
        
        Args:
            p (Vector): Point to evaluate
            radius (float): Radius of the sphere
            
        Returns:
            float: Signed distance from point to sphere surface
        """
        return p.length - radius
    
    @staticmethod
    def box(p, size):
        """
        Signed distance function for an axis-aligned box centered at origin.
        
        Args:
            p (Vector): Point to evaluate
            size (Vector): Half-extents of the box
            
        Returns:
            float: Signed distance from point to box surface
        """
        q = Vector((abs(p.x), abs(p.y), abs(p.z))) - size
        return Vector((max(q.x, 0), max(q.y, 0), max(q.z, 0))).length + min(max(q.x, max(q.y, q.z)), 0)
    
    @staticmethod
    def plane(p, normal=Vector((0, 1, 0)), h=0):
        """
        Signed distance function for an infinite plane.
        
        Args:
            p (Vector): Point to evaluate
            normal (Vector): Normal vector of the plane (must be normalized)
            h (float): Distance from origin along normal
            
        Returns:
            float: Signed distance from point to plane
        """
        return p.dot(normal) + h
    
    @staticmethod
    def cylinder(p, h, r):
        """
        Signed distance function for an infinite cylinder along Z-axis.
        
        Args:
            p (Vector): Point to evaluate
            h (float): Half height of the cylinder
            r (float): Radius of the cylinder
            
        Returns:
            float: Signed distance from point to cylinder surface
        """
        d = Vector((p.x, p.y, 0)).length - r
        return max(d, abs(p.z) - h)
    
    # ==============================================
    # Boolean Operations
    # ==============================================
    
    @staticmethod
    def union(d1, d2):
        """Union of two SDFs (minimum operation)."""
        return min(d1, d2)
    
    @staticmethod
    def subtract(d1, d2):
        """Subtract d2 from d1."""
        return max(d1, -d2)
    
    @staticmethod
    def intersect(d1, d2):
        """Intersection of two SDFs."""
        return max(d1, d2)
    
    @staticmethod
    def smooth_union(d1, d2, k):
        """Smooth union of two SDFs with smoothing factor k."""
        h = max(k - abs(d1 - d2), 0.0) / k
        return min(d1, d2) - h * h * k * 0.25
    
    # ==============================================
    # Transformations
    # ==============================================
    
    @staticmethod
    def translate(p, offset):
        """Translate a point by offset."""
        return p - offset
    
    @staticmethod
    def rotate(p, angle, axis=(0, 1, 0)):
        """Rotate a point around an axis by angle in radians."""
        axis = Vector(axis).normalized()
        s = math.sin(angle)
        c = math.cos(angle)
        m = mathutils.Matrix.Rotation(angle, 4, axis)
        return m @ p
    
    @staticmethod
    def scale(p, s):
        """Scale a point by factor s."""
        return p / s
    
    # ==============================================
    # Distance Field Operations
    # ==============================================
    
    @staticmethod
    def op_round(d, r):
        """Round the corners of an SDF by radius r."""
        return d - r
    
    @staticmethod
    def op_chamfer(d, r):
        """Chamfer the edges of an SDF by radius r."""
        return d - r
    
    @staticmethod
    def op_extrude(p, d, h):
        """Extrude a 2D SDF to 3D."""
        w = Vector((d, abs(p.z) - h))
        return min(max(w.x, w.y), 0.0) + Vector((max(w.x, 0), max(w.y, 0))).length
    
    # ==============================================
    # Utility Functions
    # ==============================================
    
    @staticmethod
    def calculate_normal(p, f, eps=None):
        """
        Calculate surface normal with improved accuracy using tetrahedron technique.
        
        Args:
            p (Vector): Point at which to calculate normal
            f (callable): SDF function
            eps (float): Epsilon for central differences (auto-scaled if None)
            
        Returns:
            Vector: Normalized surface normal
        """
        if eps is None:
            # Auto-scale epsilon based on distance to surface
            d = abs(f(p))
            eps = max(1e-5, d * 1e-4)
        
        # Tetrahedron technique for more accurate normals
        eps2 = eps * 0.5
        x = Vector((eps, -eps2, -eps2))
        y = Vector((-eps2, eps, -eps2))
        z = Vector((-eps2, -eps2, eps))
        
        return Vector((
            f(p + x) - f(p - x),
            f(p + y) - f(p - y),
            f(p + z) - f(p - z)
        )).normalized()
    
    @staticmethod
    def calculate_normal_analytical(p, f, eps=0.001):
        """
        Calculate surface normal using central differences with fallback to analytical.
        
        Args:
            p (Vector): Point at which to calculate normal
            f (callable): SDF function with optional normal_analytical method
            eps (float): Epsilon for central differences (fallback)
            
        Returns:
            Vector: Normalized surface normal
        """
        if hasattr(f, 'normal_analytical'):
            try:
                return f.normal_analytical(p)
            except:
                pass
        return SDFEngine.calculate_normal(p, f, eps)
    
    @staticmethod
    def ray_march(ro, rd, f, max_steps=100, max_dist=100.0, surface_threshold=0.001, bbox=None):
        """
        Ray march through an SDF with optional spatial acceleration.
        
        Args:
            ro (Vector): Ray origin
            rd (Vector): Ray direction (must be normalized)
            f (callable): SDF function
            max_steps (int): Maximum number of steps
            max_dist (float): Maximum distance to march
            surface_threshold (float): Distance threshold for surface intersection
            bbox (BoundingBox, optional): Bounding box for spatial acceleration
            
        Returns:
            tuple: (distance, hit_point, hit_normal, hit) where hit is a boolean
        """
        # Early exit if ray doesn't intersect bounding volume
        if bbox and not bbox.intersects(ro, rd):
            return (max_dist, None, None, False)
        
        # Initialize ray marching
        t = 0.0
        hit = False
        hit_point = None
        hit_normal = None
        
        # Adaptive step size based on distance to surface
        adaptive_eps = 0.01
        last_h = 0.0
        
        for i in range(max_steps):
            p = ro + rd * t
            d = f(p)
            
            # Check for hit
            if d < surface_threshold:
                hit = True
                hit_point = p
                hit_normal = SDFEngine.calculate_normal_analytical(p, f)
                break
            
            # Use sphere tracing with conservative step size
            step = d * 0.9  # Conservative step size
            t += step
            
            # Early termination if we've gone too far
            if t > max_dist:
                break
                
            # Adaptive step size adjustment
            if i > 3:  # Let first few steps stabilize
                if d > last_h + surface_threshold:
                    adaptive_eps *= 1.1
                else:
                    adaptive_eps *= 0.9
                adaptive_eps = max(1e-4, min(adaptive_eps, 1.0))
                
            last_h = d
                
        return (t, hit_point, hit_normal, hit)
    
    @staticmethod
    def create_bvh(objects):
        """
        Create a simple BVH (Bounding Volume Hierarchy) for a list of objects.
        Each object should have a 'bounds' method returning a BoundingBox.
        
        Args:
            objects (list): List of objects with 'bounds' method
            
        Returns:
            dict: BVH structure
        """
        if not objects:
            return None
            
        if len(objects) == 1:
            return {
                'bounds': objects[0].bounds(),
                'object': objects[0],
                'left': None,
                'right': None
            }
            
        # Find the axis with the largest span
        bounds = [obj.bounds() for obj in objects]
        centers = [b.center for b in bounds]
        
        # Calculate bounding box of all centers
        min_vals = Vector((min(p.x for p in centers),
                          min(p.y for p in centers),
                          min(p.z for p in centers)))
        max_vals = Vector((max(p.x for p in centers),
                          max(p.y for p in centers),
                          max(p.z for p in centers)))
        
        # Split on the longest axis
        axis = 0
        max_span = max_vals[0] - min_vals[0]
        for i in range(1, 3):
            span = max_vals[i] - min_vals[i]
            if span > max_span:
                max_span = span
                axis = i
        
        # Sort objects along the chosen axis
        objects.sort(key=lambda o: o.bounds().center[axis])
        mid = len(objects) // 2
        
        # Recursively build BVH
        return {
            'bounds': bounds[0].union(*bounds[1:]),
            'left': SDFEngine.create_bvh(objects[:mid]),
            'right': SDFEngine.create_bvh(objects[mid:]),
            'object': None
        }
    
    @staticmethod
    def generate_mesh(f, bbox_size=2.0, resolution=50, threshold=0.01):
        """
        Generate a mesh from an SDF using marching cubes.
        
        Args:
            f (callable): SDF function
            bbox_size (float): Size of the bounding box to evaluate
            resolution (int): Resolution of the grid
            threshold (float): Surface threshold
            
        Returns:
            tuple: (vertices, faces) for the generated mesh
        """
        # Check for required dependencies
        try:
            import numpy as np
        except ImportError:
            print("Warning: numpy not available, mesh generation disabled")
            return [], []
            
        try:
            from skimage import measure
        except ImportError:
            print("Warning: scikit-image not available, mesh generation disabled")
            return [], []
        
        # Create a grid of points
        x = np.linspace(-bbox_size, bbox_size, resolution)
        y = np.linspace(-bbox_size, bbox_size, resolution)
        z = np.linspace(-bbox_size, bbox_size, resolution)
        x, y, z = np.meshgrid(x, y, z, indexing='ij')
        
        # Evaluate SDF on the grid
        v = np.zeros_like(x)
        for i in range(len(x)):
            for j in range(len(y)):
                for k in range(len(z)):
                    p = Vector((x[i,j,k], y[i,j,k], z[i,j,k]))
                    v[i,j,k] = f(p)
        
        # Use marching cubes to extract the surface
        verts, faces, normals, _ = measure.marching_cubes(
            v, level=threshold, spacing=(x[1,0,0]-x[0,0,0], 
                                      y[0,1,0]-y[0,0,0], 
                                      z[0,0,1]-z[0,0,0]))
        
        # Transform vertices to match our coordinate system
        verts = verts - np.array([bbox_size, bbox_size, bbox_size])
        
        return verts, faces
