import bpy
from bpy.types import Panel, UIList, Menu
from bpy.utils import register_class, unregister_class
from bpy.props import (
    StringProperty,
    BoolProperty,
    IntProperty,
    FloatProperty,
    FloatVectorProperty,
    EnumProperty,
    PointerProperty,
    CollectionProperty,
)

class SDF_PT_VisualBuilder(Panel):
    """Visual Builder panel for creating SDF models without node editor"""
    bl_label = "Visual Builder"
    bl_idname = "VIEW3D_PT_arcane_sdf_builder"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Arcane SDF'
    bl_order = 1  # Show after main panel
    
    @classmethod
    def poll(cls, context):
        return True
    
    def draw(self, context):
        try:
            layout = self.layout
            scene = context.scene

            # Check if we have a node tree
            has_node_tree = hasattr(scene, 'sdf') and scene.sdf.sdf_node_tree

            if not has_node_tree:
                box = layout.box()
                box.label(text="No SDF Node Tree", icon='ERROR')
                box.operator("sdf.create_node_tree", text="Create Node Tree First", icon='ADD')
                return

            # Visual Builder Interface
            box = layout.box()
            box.label(text="Quick Build", icon='TOOL_SETTINGS')

            # Primitives Section
            prim_box = box.box()
            prim_box.label(text="Add Primitives", icon='MESH_UVSPHERE')

            # Primitive buttons in a grid
            grid = prim_box.grid_flow(columns=2, even_columns=True)
            grid.operator("sdf.add_primitive", text="Sphere").primitive_type = 'SPHERE'
            grid.operator("sdf.add_primitive", text="Box").primitive_type = 'BOX'
            grid.operator("sdf.add_primitive", text="Cylinder").primitive_type = 'CYLINDER'
            grid.operator("sdf.add_primitive", text="Torus").primitive_type = 'TORUS'

            # Operations Section
            op_box = box.box()
            op_box.label(text="Boolean Operations", icon='MOD_BOOLEAN')

            # Operation buttons
            op_grid = op_box.grid_flow(columns=3, even_columns=True)
            op_grid.operator("sdf.boolean_operation", text="Union").operation = 'UNION'
            op_grid.operator("sdf.boolean_operation", text="Subtract").operation = 'SUBTRACT'
            op_grid.operator("sdf.boolean_operation", text="Intersect").operation = 'INTERSECT'

            # Transform Section
            transform_box = box.box()
            transform_box.label(text="Transform", icon='ARROW_LEFTRIGHT')

            # Transform controls
            transform_box.operator("sdf.transform_selected", text="Move Selected")
            transform_box.operator("sdf.scale_selected", text="Scale Selected")
            transform_box.operator("sdf.rotate_selected", text="Rotate Selected")

            # Quick Actions
            action_box = box.box()
            action_box.label(text="Quick Actions", icon='PLAY')

            # Action buttons
            action_col = action_box.column(align=True)
            action_col.operator("sdf.duplicate_selected", text="Duplicate Selected")
            action_col.operator("sdf.delete_selected", text="Delete Selected")
            action_col.operator("sdf.select_all", text="Select All")
            action_col.operator("sdf.clear_selection", text="Clear Selection")

            # Viewport Controls
            viewport_box = box.box()
            viewport_box.label(text="Viewport", icon='VIEW3D')

            # Viewport toggle and quality
            viewport_box.prop(scene.sdf, "sdf_show_in_viewport", text="Show in Viewport")
            if scene.sdf.sdf_show_in_viewport:
                viewport_box.prop(scene.sdf, "sdf_viewport_quality", slider=True)
                viewport_box.operator("sdf.update_viewport", text="Update Viewport", icon='FILE_REFRESH')
        except Exception as e:
            layout.label(text=f"Panel Error: {str(e)}", icon='ERROR')

class SDF_PT_MainPanel(Panel):
    """Main panel for SDF tools"""
    bl_label = "Arcane SDF"
    bl_idname = "VIEW3D_PT_arcane_sdf"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = 'Arcane SDF'
    bl_order = 0  # Ensure it appears at the top of the category
    
    @classmethod
    def poll(cls, context):
        return True  # Always show the panel
        
    def draw(self, context):
        try:
            layout = self.layout
            scene = context.scene
            obj = context.active_object

            # Check if we have a node tree
            has_node_tree = hasattr(scene, 'sdf') and scene.sdf.sdf_node_tree

            # Main header
            col = layout.column()
            col.label(text="Arcane SDF Tools", icon='MOD_BOOLEAN')

            # Node Tree Section
            box = layout.box()
            box.label(text="Node Tree", icon='NODETREE')

            # Node tree selector
            row = box.row()
            row.prop_search(scene.sdf, "sdf_node_tree", bpy.data, "node_groups", text="")

            # Create new node tree button
            if not has_node_tree:
                box.operator("sdf.create_node_tree", text="New SDF Node Tree", icon='ADD')
            else:
                # Viewport toggle
                row = box.row(align=True)
                row.prop(scene.sdf, "sdf_show_in_viewport", text="Show in Viewport")

                # Node Editor button
                box.operator("sdf.edit_node_tree", text="Edit Node Tree", icon='NODETREE')

                # Render settings
                col = box.column(align=True)
                col.prop(scene.sdf, "sdf_viewport_quality", slider=True)
                col.prop(scene.sdf, "sdf_max_steps")
                col.prop(scene.sdf, "sdf_surface_threshold", slider=True)

            layout.separator()

            # Object Properties
            if obj and hasattr(obj, 'sdf') and obj.sdf.is_sdf_object:
                self.draw_object_properties(layout, obj)

            # Scene Settings
            layout.separator()
            self.draw_scene_settings(layout, scene)
        except Exception as e:
            layout.label(text=f"Panel Error: {str(e)}", icon='ERROR')
    
    def draw_object_properties(self, layout, obj):
        """Draw object-specific properties"""
        box = layout.box()
        box.label(text=f"{obj.name} Properties", icon='OBJECT_DATA')
        
        # Common properties
        box.prop(obj.sdf, "sdf_type", text="Type")
        
        if obj.sdf.sdf_type == 'PRIMITIVE':
            box.prop(obj.sdf, "primitive_type", text="Primitive")
            
            if obj.sdf.primitive_type in {'SPHERE', 'TORUS', 'CAPSULE', 'CONE'}:
                box.prop(obj.sdf, "radius")
            
            if obj.sdf.primitive_type in {'BOX', 'CYLINDER'}:
                box.prop(obj.sdf, "size")
        
        # Convert to mesh button
        box.operator("sdf.convert_to_mesh", icon='MESH_DATA')
    
    def draw_scene_settings(self, layout, scene):
        """Draw global scene settings"""
        box = layout.box()
        box.label(text="Viewport Settings", icon='PREFERENCES')
        
        # Viewport quality
        col = box.column(align=True)
        col.prop(scene.sdf, "viewport_resolution")
        col.prop(scene.sdf, "max_steps")
        col.prop(scene.sdf, "max_distance")
        col.prop(scene.sdf, "surface_threshold")
        
        # Toggle wireframe
        box.prop(scene.sdf, "show_wireframe")
        
        # Update button
        box.operator("sdf.update_viewport", icon='FILE_REFRESH')

class SDF_PT_MaterialPanel(Panel):
    """Panel for SDF material properties"""
    bl_label = "SDF Material"
    bl_idname = "SDF_PT_material_panel"
    bl_space_type = 'PROPERTIES'
    bl_region_type = 'WINDOW'
    bl_context = "material"
    
    @classmethod
    def poll(cls, context):
        return context.material is not None
    
    def draw(self, context):
        try:
            layout = self.layout
            mat = context.material

            if not hasattr(mat, 'sdf'):
                return

            layout.prop(mat.sdf, "base_color")
            layout.prop(mat.sdf, "metallic")
            layout.prop(mat.sdf, "roughness")
        except Exception as e:
            layout.label(text=f"Material Panel Error: {str(e)}", icon='ERROR')

# Registration
classes = (
    SDF_PT_VisualBuilder,
    SDF_PT_MainPanel,
    SDF_PT_MaterialPanel,
)

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
