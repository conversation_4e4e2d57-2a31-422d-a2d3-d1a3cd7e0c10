import bpy
from bpy.types import Operator, NodeTree
from bpy.props import (
    <PERSON><PERSON><PERSON>ty,
    <PERSON><PERSON><PERSON>roperty,
    IntProperty,
    FloatProperty,
    FloatVectorProperty,
    EnumProperty,
    PointerProperty,
)
from bpy.utils import register_class, unregister_class

# Visual Builder Operators
class SDF_OT_AddPrimitive(Operator):
    """Add a primitive to the SDF node tree"""
    bl_idname = "sdf.add_primitive"
    bl_label = "Add Primitive"
    bl_options = {'REGISTER', 'UNDO'}
    
    primitive_type: EnumProperty(
        name="Type",
        description="Type of primitive to add",
        items=[
            ('SPHERE', "Sphere", "Add a sphere primitive"),
            ('BOX', "Box", "Add a box primitive"),
            ('CYLINDER', "Cylinder", "Add a cylinder primitive"),
            ('TORUS', "Torus", "Add a torus primitive"),
        ],
        default='SPHERE',
    )
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Create the appropriate node
        if self.primitive_type == 'SPHERE':
            node = node_tree.nodes.new('SDFNode_Sphere')
            node.location = (0, 0)
        elif self.primitive_type == 'BOX':
            node = node_tree.nodes.new('SDFNode_Box')
            node.location = (0, 0)
        elif self.primitive_type == 'CYLINDER':
            node = node_tree.nodes.new('SDFNode_Cylinder')
            node.location = (0, 0)
        elif self.primitive_type == 'TORUS':
            node = node_tree.nodes.new('SDFNode_Torus')
            node.location = (0, 0)
        
        # Ensure we have an output node
        output_node = self._ensure_output_node(node_tree)
        
        # Connect the new node to the output
        if output_node and hasattr(node, 'outputs') and node.outputs:
            node_tree.links.new(node.outputs[0], output_node.inputs[0])
        
        # Select the new node
        for n in node_tree.nodes:
            n.select = False
        node.select = True
        node_tree.nodes.active = node
        
        self.report({'INFO'}, f"Added {self.primitive_type.lower()} primitive")
        return {'FINISHED'}
    
    def _ensure_output_node(self, node_tree):
        """Ensure there's an output node in the tree"""
        # Look for existing output node
        for node in node_tree.nodes:
            if node.bl_idname == 'NodeGroupOutput':
                return node
        
        # Create output node if none exists
        output_node = node_tree.nodes.new('NodeGroupOutput')
        output_node.location = (300, 0)
        return output_node

class SDF_OT_BooleanOperation(Operator):
    """Perform boolean operation on selected nodes"""
    bl_idname = "sdf.boolean_operation"
    bl_label = "Boolean Operation"
    bl_options = {'REGISTER', 'UNDO'}
    
    operation: EnumProperty(
        name="Operation",
        description="Boolean operation to perform",
        items=[
            ('UNION', "Union", "Combine selected nodes"),
            ('SUBTRACT', "Subtract", "Subtract selected nodes"),
            ('INTERSECT', "Intersect", "Find intersection of selected nodes"),
        ],
        default='UNION',
    )
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Get selected nodes
        selected_nodes = [n for n in node_tree.nodes if n.select]
        
        if len(selected_nodes) < 2:
            self.report({'ERROR'}, "Select at least 2 nodes for boolean operation")
            return {'CANCELLED'}
        
        # Create the appropriate boolean node
        if self.operation == 'UNION':
            bool_node = node_tree.nodes.new('SDFNode_Union')
        elif self.operation == 'SUBTRACT':
            bool_node = node_tree.nodes.new('SDFNode_Subtraction')
        elif self.operation == 'INTERSECT':
            bool_node = node_tree.nodes.new('SDFNode_Intersection')
        
        # Position the boolean node
        bool_node.location = (0, 0)
        
        # Connect selected nodes to the boolean node
        for i, node in enumerate(selected_nodes[:2]):  # Only use first 2 nodes
            if i == 0:
                node_tree.links.new(node.outputs[0], bool_node.inputs[0])
            else:
                node_tree.links.new(node.outputs[0], bool_node.inputs[1])
        
        # Ensure we have an output node and connect the boolean node
        output_node = self._ensure_output_node(node_tree)
        if output_node and hasattr(bool_node, 'outputs') and bool_node.outputs:
            node_tree.links.new(bool_node.outputs[0], output_node.inputs[0])
        
        # Select the boolean node
        for n in node_tree.nodes:
            n.select = False
        bool_node.select = True
        node_tree.nodes.active = bool_node
        
        self.report({'INFO'}, f"Applied {self.operation.lower()} operation")
        return {'FINISHED'}
    
    def _ensure_output_node(self, node_tree):
        """Ensure there's an output node in the tree"""
        # Look for existing output node
        for node in node_tree.nodes:
            if node.bl_idname == 'NodeGroupOutput':
                return node
        
        # Create output node if none exists
        output_node = node_tree.nodes.new('NodeGroupOutput')
        output_node.location = (300, 0)
        return output_node

class SDF_OT_TransformSelected(Operator):
    """Transform selected nodes"""
    bl_idname = "sdf.transform_selected"
    bl_label = "Transform Selected"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Get selected nodes
        selected_nodes = [n for n in node_tree.nodes if n.select]
        
        if not selected_nodes:
            self.report({'ERROR'}, "No nodes selected")
            return {'CANCELLED'}
        
        # Create a translate node for each selected node
        for node in selected_nodes:
            translate_node = node_tree.nodes.new('SDFNode_Translate')
            translate_node.location = (node.location.x + 200, node.location.y)
            
            # Connect the original node to the translate node
            if hasattr(node, 'outputs') and node.outputs:
                node_tree.links.new(node.outputs[0], translate_node.inputs[0])
        
        self.report({'INFO'}, "Added transform nodes")
        return {'FINISHED'}

class SDF_OT_ScaleSelected(Operator):
    """Scale selected nodes"""
    bl_idname = "sdf.scale_selected"
    bl_label = "Scale Selected"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        # For now, this is a placeholder - scaling would require a scale node
        self.report({'INFO'}, "Scale operation not yet implemented")
        return {'FINISHED'}

class SDF_OT_RotateSelected(Operator):
    """Rotate selected nodes"""
    bl_idname = "sdf.rotate_selected"
    bl_label = "Rotate Selected"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        # For now, this is a placeholder - rotation would require a rotate node
        self.report({'INFO'}, "Rotate operation not yet implemented")
        return {'FINISHED'}

class SDF_OT_DuplicateSelected(Operator):
    """Duplicate selected nodes"""
    bl_idname = "sdf.duplicate_selected"
    bl_label = "Duplicate Selected"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Get selected nodes
        selected_nodes = [n for n in node_tree.nodes if n.select]
        
        if not selected_nodes:
            self.report({'ERROR'}, "No nodes selected")
            return {'CANCELLED'}
        
        # Duplicate each selected node
        for node in selected_nodes:
            # Create a new node of the same type
            new_node = node_tree.nodes.new(node.bl_idname)
            new_node.location = (node.location.x + 100, node.location.y + 100)
            
            # Copy properties
            for prop in node.bl_rna.properties:
                if prop.identifier not in ['name', 'type', 'location']:
                    try:
                        setattr(new_node, prop.identifier, getattr(node, prop.identifier))
                    except:
                        pass
        
        self.report({'INFO'}, f"Duplicated {len(selected_nodes)} nodes")
        return {'FINISHED'}

class SDF_OT_DeleteSelected(Operator):
    """Delete selected nodes"""
    bl_idname = "sdf.delete_selected"
    bl_label = "Delete Selected"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Get selected nodes
        selected_nodes = [n for n in node_tree.nodes if n.select]
        
        if not selected_nodes:
            self.report({'ERROR'}, "No nodes selected")
            return {'CANCELLED'}
        
        # Delete selected nodes
        for node in selected_nodes:
            node_tree.nodes.remove(node)
        
        self.report({'INFO'}, f"Deleted {len(selected_nodes)} nodes")
        return {'FINISHED'}

class SDF_OT_SelectAll(Operator):
    """Select all nodes"""
    bl_idname = "sdf.select_all"
    bl_label = "Select All"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Select all nodes
        for node in node_tree.nodes:
            node.select = True
        
        self.report({'INFO'}, "Selected all nodes")
        return {'FINISHED'}

class SDF_OT_ClearSelection(Operator):
    """Clear node selection"""
    bl_idname = "sdf.clear_selection"
    bl_label = "Clear Selection"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        scene = context.scene
        node_tree = scene.sdf.sdf_node_tree
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Clear selection
        for node in node_tree.nodes:
            node.select = False
        
        self.report({'INFO'}, "Cleared selection")
        return {'FINISHED'}

class SDF_OT_CreatePrimitive(Operator):
    """Create a new SDF primitive"""
    bl_idname = "sdf.create_primitive"
    bl_label = "Create SDF Primitive"
    bl_options = {'REGISTER', 'UNDO'}
    
    primitive_type: EnumProperty(
        name="Type",
        description="Type of SDF primitive to create",
        items=[
            ('SPHERE', "Sphere", "Create a sphere primitive"),
            ('BOX', "Box", "Create a box primitive"),
            ('CYLINDER', "Cylinder", "Create a cylinder primitive"),
            ('TORUS', "Torus", "Create a torus primitive"),
            ('CAPSULE', "Capsule", "Create a capsule primitive"),
            ('CONE', "Cone", "Create a cone primitive"),
        ],
        default='SPHERE',
    )
    
    radius: FloatProperty(
        name="Radius",
        description="Radius of the primitive",
        default=1.0,
        min=0.001,
    )
    
    def execute(self, context):
        # Create a new empty object
        mesh = bpy.data.meshes.new(name=f"SDF_{self.primitive_type.capitalize()}")
        obj = bpy.data.objects.new(mesh.name, mesh)
        
        # Link to scene
        context.collection.objects.link(obj)
        
        # Set as active object
        context.view_layer.objects.active = obj
        obj.select_set(True)
        
        # Set SDF properties
        obj.sdf.is_sdf_object = True
        obj.sdf.sdf_type = 'PRIMITIVE'
        obj.sdf.primitive_type = self.primitive_type
        obj.sdf.radius = self.radius
        
        # Set default size based on primitive type
        if self.primitive_type == 'BOX':
            obj.sdf.size = (2.0, 2.0, 2.0)
        elif self.primitive_type == 'CYLINDER':
            obj.sdf.size = (1.0, 1.0, 2.0)  # Height in Z
        
        # Ensure viewport updates
        from .shaders import SDFRenderer
        SDFRenderer.enable()
        
        # Force viewport update
        context.area.tag_redraw()
        
        self.report({'INFO'}, f"Created SDF {self.primitive_type.lower()}")
        return {'FINISHED'}
    
    def invoke(self, context, event):
        wm = context.window_manager
        return wm.invoke_props_dialog(self)

class SDF_OT_ConvertToMesh(Operator):
    """Convert SDF object to mesh"""
    bl_idname = "sdf.convert_to_mesh"
    bl_label = "Convert to Mesh"
    bl_options = {'REGISTER', 'UNDO'}
    
    resolution: IntProperty(
        name="Resolution",
        description="Resolution of the generated mesh",
        default=64,
        min=8,
        max=256,
    )
    
    @classmethod
    def poll(cls, context):
        return (
            context.active_object is not None and
            hasattr(context.active_object, 'sdf') and
            context.active_object.sdf.is_sdf_object
        )
    
    def execute(self, context):
        obj = context.active_object
        
        # TODO: Implement actual SDF to mesh conversion
        # This is a placeholder that just creates a simple mesh
        
        if obj.sdf.sdf_type == 'PRIMITIVE':
            if obj.sdf.primitive_type == 'SPHERE':
                bpy.ops.mesh.primitive_uv_sphere_add(
                    radius=obj.sdf.radius,
                    segments=self.resolution,
                    ring_count=self.resolution//2
                )
            elif obj.sdf.primitive_type == 'BOX':
                bpy.ops.mesh.primitive_cube_add(
                    size=1.0,
                    scale=obj.sdf.size
                )
            # Add more primitive conversions...
        
        # Remove the original SDF object
        bpy.data.objects.remove(obj, do_unlink=True)
        
        self.report({'INFO'}, "Converted SDF to mesh")
        return {'FINISHED'}

class SDF_OT_UpdateViewport(Operator):
    """Update the SDF viewport rendering"""
    bl_idname = "sdf.update_viewport"
    bl_label = "Update Viewport"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        from .shaders import SDFRenderer
        
        # Reset and reinitialize the renderer
        SDFRenderer.reset()
        SDFRenderer.enable()
        
        # Trigger viewport update
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
                
        self.report({'INFO'}, "Viewport updated")
        return {'FINISHED'}

class SDF_OT_CreateNodeTree(Operator):
    """Create a new SDF node tree"""
    bl_idname = "sdf.create_node_tree"
    bl_label = "New SDF Node Tree"
    bl_options = {'REGISTER', 'UNDO'}
    
    name: StringProperty(
        name="Name",
        description="Name for the new node tree",
        default="SDF Node Tree"
    )
    
    def execute(self, context):
        # Create a new node tree
        node_tree = bpy.data.node_groups.new(self.name, 'SDFNodeTree')
        
        # Create an output node for the tree
        output_node = node_tree.nodes.new('NodeGroupOutput')
        output_node.location = (300, 0)
        
        # Set as active node tree through the sdf property group
        context.scene.sdf.sdf_node_tree = node_tree
        
        self.report({'INFO'}, f"Created SDF node tree: {node_tree.name}")
        return {'FINISHED'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_props_dialog(self)

class SDF_OT_EditNodeTree(Operator):
    """Open the node editor with the active SDF node tree"""
    bl_idname = "sdf.edit_node_tree"
    bl_label = "Edit Node Tree"
    bl_options = {'REGISTER'}
    
    @classmethod
    def poll(cls, context):
        return (hasattr(context.scene, 'sdf') and 
                context.scene.sdf.sdf_node_tree is not None)
    
    def execute(self, context):
        print("SDF_OT_EditNodeTree.execute() called")
        
        # Get the node tree
        node_tree = context.scene.sdf.sdf_node_tree
        print(f"Node tree: {node_tree}")
        
        if not node_tree:
            self.report({'ERROR'}, "No SDF node tree selected")
            return {'CANCELLED'}
        
        # Simple approach: just switch to node editor and set the tree
        try:
            # Find or create a node editor area
            node_editor_area = None
            
            # First, try to find an existing node editor
            for area in context.screen.areas:
                if area.type == 'NODE_EDITOR':
                    node_editor_area = area
                    break
            
            # If no node editor exists, create one
            if not node_editor_area:
                # Split the current area
                bpy.ops.screen.area_split(direction='VERTICAL', factor=0.5)
                # The newly created area should be the node editor
                node_editor_area = context.screen.areas[-1]
                node_editor_area.type = 'NODE_EDITOR'
            
            # Set the node tree in the node editor
            space = node_editor_area.spaces.active
            space.node_tree = node_tree
            space.tree_type = 'SDFNodeTree'
            
            self.report({'INFO'}, f"Opened node editor with {node_tree.name}")
            return {'FINISHED'}
                
        except Exception as e:
            print(f"Error in EditNodeTree: {e}")
            self.report({'ERROR'}, f"Error: {e}")
            return {'CANCELLED'}

# Registration
classes = (
    # Visual Builder Operators
    SDF_OT_AddPrimitive,
    SDF_OT_BooleanOperation,
    SDF_OT_TransformSelected,
    SDF_OT_ScaleSelected,
    SDF_OT_RotateSelected,
    SDF_OT_DuplicateSelected,
    SDF_OT_DeleteSelected,
    SDF_OT_SelectAll,
    SDF_OT_ClearSelection,
    
    # Original Operators
    SDF_OT_CreatePrimitive,
    SDF_OT_ConvertToMesh,
    SDF_OT_UpdateViewport,
    SDF_OT_CreateNodeTree,
    SDF_OT_EditNodeTree,
)

def register():
    from bpy.utils import register_class
    
    # Register all operator classes
    for cls in classes:
        try:
            register_class(cls)
        except ValueError as e:
            # If class is already registered, unregister it first
            if "already registered" in str(e):
                try:
                    unregister_class(cls)
                    register_class(cls)
                except Exception as e:
                    print(f"Error re-registering {cls.__name__}: {e}")
            else:
                print(f"Error registering {cls.__name__}: {e}")
    
    # Update keymaps if needed
    # add_keymap()

def unregister():
    from bpy.utils import unregister_class
    
    # Unregister all operator classes in reverse order
    for cls in reversed(classes):
        try:
            unregister_class(cls)
        except RuntimeError as e:
            if "not registered" in str(e):
                pass  # Already unregistered
            else:
                print(f"Error unregistering {cls.__name__}: {e}")
    
    # Clean up keymaps if they were registered
    # remove_keymap()
    
    # Force viewport update
    for window in bpy.context.window_manager.windows:
        for area in window.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
